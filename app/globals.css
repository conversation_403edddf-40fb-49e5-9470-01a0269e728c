@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  max-width: 100vw;
}

/* Prevent horizontal scrolling on mobile */
html {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* Ensure text doesn't cause horizontal overflow */
h1, h2, h3, h4, h5, h6, p, span, div {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Placeholder text visibility fixes */
input::placeholder,
textarea::placeholder {
  color: #6b7280; /* gray-500 for better visibility */
  opacity: 1;
}

/* Specific fixes for form inputs */
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="text"]::placeholder {
  color: #4b5563; /* gray-600 for even better contrast */
  opacity: 1;
}

/* Dark background form fixes */
.bg-white input::placeholder {
  color: #6b7280;
}

/* Focus state improvements */
input:focus::placeholder,
textarea:focus::placeholder {
  color: #9ca3af; /* gray-400 when focused */
  opacity: 0.7;
}

/* Mobile and tablet specific overrides */
@media (max-width: 1024px) {
  input::placeholder,
  textarea::placeholder {
    color: #6b7280 !important;
    opacity: 1 !important;
  }

  input[type="email"]::placeholder,
  input[type="password"]::placeholder,
  input[type="text"]::placeholder {
    color: #4b5563 !important;
    opacity: 1 !important;
  }

  .bg-white input::placeholder {
    color: #6b7280 !important;
  }

  input:focus::placeholder,
  textarea:focus::placeholder {
    color: #9ca3af !important;
    opacity: 0.7 !important;
  }

  /* Ensure textarea text is always visible on mobile - but exclude diary writing interface */
  textarea:not([class*="bg-slate-"]):not([class*="bg-white"]):not([class*="bg-transparent"]) {
    color: #111827 !important; /* gray-900 for high contrast */
    background-color: #ffffff !important;
  }

  /* Ensure textarea text stays visible even in dark containers - but allow intentional dark mode */
  textarea:not([class*="bg-"]):not([class*="text-white"]):not([class*="bg-transparent"]) {
    background-color: #ffffff !important;
    color: #111827 !important;
  }

  /* Improve text contrast on mobile for prose content - light mode only */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }

  /* Improve text contrast on mobile for prose content */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }



.ebook-reader-content li {
  /* List items should inherit color */
  color: inherit !important;
}

/* Ensure zen mode text is visible on mobile devices */
@media (max-width: 768px) {
  .prose-invert p,
  .prose-invert li,
  .prose-invert span,
  .prose-invert div {
    color: #e5e7eb !important; /* Softer gray-200 instead of bright white */
  }

  .prose-invert h1,
  .prose-invert h2,
  .prose-invert h3,
  .prose-invert h4,
  .prose-invert h5,
  .prose-invert h6 {
    color: #f3f4f6 !important; /* Slightly softer than pure white */
  }
}

  /* Ensure button colors are consistent on mobile and tablet */
  .bg-blue-600 {
    background-color: #2563eb !important;
  }

  .bg-purple-600 {
    background-color: #9333ea !important;
  }

  .bg-gray-100 {
    background-color: #f3f4f6 !important;
  }

  .bg-gray-800 {
    background-color: #1f2937 !important;
  }

  .bg-green-600 {
    background-color: #16a34a !important;
  }

  .bg-emerald-600 {
    background-color: #059669 !important;
  }

  /* Improve button text contrast on mobile and tablet */
  button .text-gray-700 {
    color: #374151 !important;
  }

  button .text-green-700 {
    color: #15803d !important;
  }

  /* Force consistent button backgrounds and text colors */
  button.bg-white,
  .bg-white button {
    background-color: #ffffff !important;
    color: #111827 !important;
  }

  /* Ensure outline buttons have proper contrast */
  button[class*="outline"],
  .variant-outline {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
  }

  button[class*="outline"]:hover,
  .variant-outline:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  /* Fix specific button visibility issues on mobile at night */
  /* Sign in button */
  button.bg-gray-800,
  .bg-gray-800 {
    background-color: #1f2937 !important;
    color: #ffffff !important;
  }

  button.bg-gray-800:hover,
  .bg-gray-800:hover {
    background-color: #374151 !important;
    color: #ffffff !important;
  }

  /* Write Entry and Publish Book buttons */
  button.bg-blue-600,
  .bg-blue-600 {
    background-color: #2563eb !important;
    color: #ffffff !important;
  }

  button.bg-blue-600:hover,
  .bg-blue-600:hover {
    background-color: #1d4ed8 !important;
    color: #ffffff !important;
  }

  button.bg-purple-600,
  .bg-purple-600 {
    background-color: #9333ea !important;
    color: #ffffff !important;
  }

  button.bg-purple-600:hover,
  .bg-purple-600:hover {
    background-color: #7c3aed !important;
    color: #ffffff !important;
  }

  /* Prevent any system dark mode interference on buttons */
  button,
  [role="button"] {
    color-scheme: light !important;
  }

  /* Force button text colors to be visible */
  button.text-white,
  .text-white {
    color: #ffffff !important;
  }

  /* Ensure Google OAuth button stays visible */
  .bg-white.border-gray-300 {
    background-color: #ffffff !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
  }

  /* Force light backgrounds for book cards on mobile and tablet to prevent automatic dark mode */
  .bg-white {
    background-color: #ffffff !important;
  }

  /* Ensure book card content stays light on mobile and tablet */
  [data-testid="card-content"],
  .card-content,
  .book-card-content {
    background-color: #ffffff !important;
    color: #111827 !important;
  }

  /* Specifically target book cards to prevent any dark mode interference */
  .books-page .bg-white,
  .books-page [class*="card"],
  .books-page [class*="Card"] {
    background-color: #ffffff !important;
  }

  /* Ensure text in book cards stays dark */
  .books-page .text-gray-900,
  .books-page .text-gray-600,
  .books-page .text-gray-700 {
    color: #111827 !important;
  }

  /* Prevent any browser-level dark mode interference on book information */
  .books-page * {
    color-scheme: light !important;
  }

  /* Override any potential system dark mode on book cards */
  .books-page [class*="Card"],
  .books-page [class*="card"] {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #e5e7eb !important;
  }

  /* Fix e-reader navigation button contrast issues */
  .ebook-reader button,
  .immersive-reader button,
  [class*="reader"] button {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
  }

  .ebook-reader button:hover,
  .immersive-reader button:hover,
  [class*="reader"] button:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  /* Specifically fix Previous/Next chapter buttons */
  button[class*="outline"]:disabled {
    background-color: #f9fafb !important;
    color: #9ca3af !important;
    border-color: #e5e7eb !important;
  }
}

/* Removed system dark mode detection to ensure consistent light mode appearance */
/* Individual components handle their own dark mode states when needed */

/* Simple mobile button visibility fixes for night mode */
@media (max-width: 1024px) {
  /* Prevent system dark mode from affecting buttons */
  button {
    color-scheme: light !important;
  }

  /* Fix sign in button (gray-800) */
  button.bg-gray-800 {
    background-color: #1f2937 !important;
    color: #ffffff !important;
  }

  /* Fix Write Entry button (blue-600) */
  button.bg-blue-600 {
    background-color: #2563eb !important;
    color: #ffffff !important;
  }

  /* Fix Publish Book button (purple-600) */
  button.bg-purple-600 {
    background-color: #9333ea !important;
    color: #ffffff !important;
  }

  /* Fix outline buttons (Create Account) */
  button[class*="outline"] {
    background-color: #ffffff !important;
    color: #111827 !important;
    border: 1px solid #d1d5db !important;
  }

  /* Ensure user names and important text stay visible on mobile - but exclude diary writing interface and zen mode */
  .text-gray-900:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  .text-gray-800:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  .text-gray-700:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  .text-gray-600:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #111827 !important;
  }

  /* Specifically target user names and writer names */
  h1.text-gray-800,
  p.text-gray-900,
  .font-medium.text-gray-900,
  .font-medium.text-gray-800 {
    color: #111827 !important;
  }

  /* Prevent system dark mode from affecting any text elements */
  * {
    color-scheme: light !important;
  }

  /* Ensure all text elements maintain proper contrast */
  h1, h2, h3, h4, h5, h6, p, span, div, a {
    color: inherit !important;
  }

  /* Force specific gray text classes to stay dark - but exclude diary writing interface and zen mode */
  [class*="text-gray-"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #111827 !important;
  }

  /* FORCE ALL TEXT IN ZEN MODE TO BE WHITE - OVERRIDE EVERYTHING */
  .zen-mode *,
  .zen-mode h1,
  .zen-mode h2,
  .zen-mode h3,
  .zen-mode h4,
  .zen-mode p,
  .zen-mode span,
  .zen-mode div,
  .zen-mode label,
  .zen-mode button,
  .zen-mode [class*="text-gray-"],
  .zen-mode [class*="text-black"],
  .zen-mode .text-gray-900,
  .zen-mode .text-gray-800,
  .zen-mode .text-gray-700,
  .zen-mode .text-gray-600,
  .zen-mode .text-gray-500 {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Force zen mode white text colors to work with proper opacity */
  .zen-mode .text-white\/90 {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .zen-mode .text-white\/80 {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .zen-mode .text-white\/60 {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .zen-mode .text-white\/50 {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  .zen-mode .text-white\/40 {
    color: rgba(255, 255, 255, 0.4) !important;
  }

  .zen-mode .text-white\/30 {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  /* Ensure user names and important text stay visible on mobile - but exclude diary writing interface and zen mode */
  .text-gray-900:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  [class*="text-gray-9"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #111827 !important;
  }

  .text-gray-800:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  [class*="text-gray-8"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #1f2937 !important;
  }

  .text-gray-700:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  [class*="text-gray-7"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #374151 !important;
  }

  .text-gray-600:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  [class*="text-gray-6"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #4b5563 !important;
  }

  .text-gray-500:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *),
  [class*="text-gray-5"]:not([class*="text-white"]):not(.bg-slate-900 *):not(.bg-slate-800 *) {
    color: #6b7280 !important;
  }

  /* Specifically ensure user names in posts are visible */
  a[class*="text-gray-900"],
  button[class*="text-gray-900"],
  .font-medium.text-gray-900,
  .font-bold.text-gray-900 {
    color: #111827 !important;
  }

  /* Force specific button styling regardless of dark mode classes */
  button[class*="bg-gray-800"],
  button[class*="bg-blue-600"],
  button[class*="bg-purple-600"] {
    background-color: var(--btn-bg, #1f2937) !important;
    color: #ffffff !important;
  }

  /* Set button background colors */
  .bg-gray-800 {
    --btn-bg: #1f2937;
  }

  .bg-blue-600 {
    --btn-bg: #2563eb;
  }

  .bg-purple-600 {
    --btn-bg: #9333ea;
  }

  .bg-amber-600 {
    --btn-bg: #d97706;
  }

  /* Fix amber button specifically */
  button.bg-amber-600 {
    background-color: #d97706 !important;
    color: #ffffff !important;
  }

  /* Fix gradient buttons with white text on mobile */
  button[class*="from-purple-600"],
  button[class*="bg-gradient-to-r"] {
    color: #ffffff !important;
  }

  /* Ensure mobile navigation buttons are touchable */
  button {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    -webkit-user-select: none;
    min-height: 44px; /* iOS minimum touch target */
  }

  /* Fix paywall text visibility on mobile dark mode */
  .bg-slate-700 h3,
  .bg-slate-700 p,
  .bg-slate-800 h3,
  .bg-slate-800 p,
  [class*="bg-slate-7"] h3,
  [class*="bg-slate-7"] p,
  [class*="bg-slate-8"] h3,
  [class*="bg-slate-8"] p {
    color: #ffffff !important;
  }

  /* Ensure paywall text is visible in dark backgrounds */
  .bg-slate-700\/50 h3,
  .bg-slate-700\/50 p,
  .bg-slate-800\/50 h3,
  .bg-slate-800\/50 p {
    color: #ffffff !important;
  }

}

/* Hide navigation when in e-reader mode */
body.ereader-mode nav {
  display: none !important;
}
