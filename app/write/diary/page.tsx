"use client"

import { useState, useEffect, Suspense, useTransition } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { PhotoUpload } from "@/components/PhotoUpload"
import { VideoUpload } from "@/components/VideoUpload"
import { Button } from "@/components/ui/button"
import { SmartTypography, useSmartFormatting, getTypographyMetrics } from "@/components/SmartTypography"
import { InvitePrompt } from "@/components/InvitePrompt"
import { SmartInviteModal } from "@/components/SmartInviteModal"
import { FirstPostModal } from "@/components/FirstPostModal"
import { usePostPublishActions } from "@/hooks/usePostPublishActions"
import { useDeviceCapabilities } from "@/hooks/useDeviceDetection"
import { DeleteConfirmModal, useDeleteConfirm } from "@/components/DeleteConfirmModal"
import { GoalSettingForm, type StoryVentureGoal } from "@/components/StoryVentures"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  is_hidden: boolean
  created_at: string
  edited_at?: string
  is_story_venture?: boolean
  funding_goal_cents?: number
  funding_deadline?: string
  story_venture_description?: string
  show_funding_publicly?: boolean
}

function WritePageContent() {
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [isFree, setIsFree] = useState(false)
  const [isHidden, setIsHidden] = useState(false)

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [isPending, startTransition] = useTransition()
  const [user, setUser] = useState<{ id: string; role: string } | null>(null)
  const [editingEntry, setEditingEntry] = useState<DiaryEntry | null>(null)
  const [error, setError] = useState("")
  const [showPartTwoOption, setShowPartTwoOption] = useState(false)

  const [currentTime, setCurrentTime] = useState(new Date())


  // Flow state tracking
  const [isInFlow, setIsInFlow] = useState(false)
  const [zenMode, setZenMode] = useState(false)
  const [lastTypingTime, setLastTypingTime] = useState(Date.now())
  const [typingVelocity, setTypingVelocity] = useState(0)
  const [wordCount, setWordCount] = useState(0)
  const [sessionStartTime] = useState(Date.now())
  const [focusDepth, setFocusDepth] = useState(0)
  const [showPreview, setShowPreview] = useState(false)
  const [typographyMetrics, setTypographyMetrics] = useState<{ readingTime: number; wordCount: number; charCount: number } | null>(null)
  const [hasPhotos, setHasPhotos] = useState(false)
  const [hasVideo, setHasVideo] = useState(false)
  const [showFirstPostModal, setShowFirstPostModal] = useState(false)
  const [storyVentureGoal, setStoryVentureGoal] = useState<StoryVentureGoal | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const editId = searchParams.get('edit')
  const supabase = createSupabaseClient()
  const deviceCapabilities = useDeviceCapabilities()

  // Delete confirmation modal
  const deleteConfirm = useDeleteConfirm()
  const { triggerFirstPostModal } = usePostPublishActions()

  // NO AUTO-SAVE - Manual save only

  // Check for character limit and show Part 2 option
  useEffect(() => {
    setShowPartTwoOption(content.length > 18000) // Show warning at 18k chars (2k before limit)
  }, [content])

  // Flow state detection - the psychological brilliance
  useEffect(() => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0)
    setWordCount(words.length)

    const now = Date.now()

    // Calculate typing velocity (words per minute)
    const sessionMinutes = (now - sessionStartTime) / 60000
    const currentVelocity = sessionMinutes > 0 ? words.length / sessionMinutes : 0
    setTypingVelocity(currentVelocity)

    // Flow state detection algorithm
    const timeSinceLastType = now - lastTypingTime
    const isTypingConsistently = timeSinceLastType < 3000 // Less than 3 seconds since last keystroke
    const hasGoodVelocity = currentVelocity > 15 // More than 15 WPM
    const hasSubstantialContent = words.length > 50 // At least 50 words
    const isInFlowState = isTypingConsistently && hasGoodVelocity && hasSubstantialContent

    setIsInFlow(isInFlowState)

    // Auto-zen mode when deep in flow (removed focusDepth to prevent infinite loop)
    if (isInFlowState && words.length > 100 && !zenMode) {
      setFocusDepth(prev => {
        const newDepth = prev + 1
        if (newDepth > 3) { // After sustained flow, suggest zen mode
          setTimeout(() => setZenMode(true), 2000)
        }
        return newDepth
      })
    }

    // Update typography metrics
    if (content.length > 50) {
      const metrics = getTypographyMetrics(content)
      setTypographyMetrics(metrics)
    }
  }, [content, sessionStartTime, zenMode]) // Removed focusDepth from dependencies to prevent infinite loop

  // Track typing time separately to avoid infinite loops
  useEffect(() => {
    setLastTypingTime(Date.now())
  }, [content])

  // Smart formatting removed from real-time editing - will only apply on publish

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // Update every minute

    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    const initializePage = async () => {
      setLoading(true)
      
      // Check authentication
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      
      if (authError || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile || (profile.role !== 'user' && profile.role !== 'admin')) {
        router.push('/')
        return
      }

      setUser(profile)





      // Check for Part 2 creation or pre-filled title
      const part2Param = searchParams.get('part2')
      const titleParam = searchParams.get('title')

      if (part2Param && titleParam) {
        setTitle(decodeURIComponent(titleParam))
        setContent('') // Start fresh for Part 2
      }

      // If editing, load the entry
      if (editId) {
        const { data: entry, error: entryError } = await supabase
          .from("diary_entries")
          .select("*")
          .eq("id", editId)
          .eq("user_id", authUser.id)
          .single()

        if (entryError || !entry) {
          setError("Entry not found or access denied")
        } else {
          setEditingEntry(entry)
          setTitle(entry.title)
          setContent(entry.body_md)
          setIsFree(entry.is_free)
          setIsHidden(entry.is_hidden)

          // Load Story Ventures data if it exists
          if (entry.is_story_venture) {
            setStoryVentureGoal({
              isEnabled: true,
              goalAmount: entry.funding_goal_cents ? entry.funding_goal_cents / 100 : 0,
              deadline: entry.funding_deadline ? new Date(entry.funding_deadline) : undefined,
              description: entry.story_venture_description || "",
              showPublicly: entry.show_funding_publicly ?? true
            })
          }
        }
      }

      setLoading(false)
    }

    initializePage()
  }, [editId, router, supabase])

  const handleSave = (publish: boolean = false, skipValidation = false) => {
    if (!skipValidation && !title.trim()) {
      setError("Title is required")
      return
    }

    // Allow photo-only posts - content is optional
    if (!content.trim() && !editingEntry?.id) {
      // For new entries without content, check if they have photos
      // This will be validated after auto-save creates the entry
    }

    if (content.length > 20000) {
      setError("Content must be 20,000 characters or less")
      return
    }

    startTransition(async () => {
      setError("")
      let hasError = false

      try {
        // Apply smart formatting only when publishing
        let finalContent = content.trim()
        if (publish && finalContent) {
          // Smart formatting for published content
          finalContent = finalContent
            .replace(/\n{3,}/g, '\n\n') // Fix excessive line breaks
            .replace(/\s+$/gm, '') // Remove trailing spaces
            .replace(/([.!?])\s{3,}/g, '$1  ') // Max 2 spaces after sentences
            .replace(/([;:,])\s{2,}/g, '$1 ') // Max 1 space after other punctuation
        }

        const entryData = {
          title: title.trim(),
          body_md: finalContent,
          is_free: isFree,
          is_hidden: !publish,
          // Story Ventures fields
          is_story_venture: storyVentureGoal?.isEnabled || false,
          funding_goal_cents: storyVentureGoal?.isEnabled ? Math.round(storyVentureGoal.goalAmount * 100) : null,
          funding_deadline: storyVentureGoal?.deadline ? storyVentureGoal.deadline.toISOString() : null,
          story_venture_description: storyVentureGoal?.description || null,
          show_funding_publicly: storyVentureGoal?.showPublicly ?? true
        }

        console.log("Attempting to save entry with data:", entryData)
        console.log("User:", user)
        console.log("Editing entry:", editingEntry)

        if (editingEntry) {
          // Update existing entry
          const { error: updateError } = await supabase
            .from("diary_entries")
            .update(entryData)
            .eq("id", editingEntry.id)
            .eq("user_id", user.id)

          if (updateError) {
            console.error("Update error:", updateError)
            console.error("Update error details:", JSON.stringify(updateError, null, 2))
            console.error("Entry data being updated:", entryData)
            console.error("Entry ID:", editingEntry.id)
            console.error("User ID:", user.id)
            setError(`Failed to update entry: ${updateError.message || 'Unknown error'}`)
            hasError = true
          }
        } else {
          // Create new entry
          const { data: newEntry, error: insertError } = await supabase
            .from("diary_entries")
            .insert({
              ...entryData,
              user_id: user.id
            })
            .select()
            .single()

          if (insertError) {
            console.error("Insert error:", insertError)
            if (insertError.message.includes("idx_one_free_entry_per_writer")) {
              setError("You can only have one free entry. Please uncheck 'Make this entry free' or edit your existing free entry.")
            } else {
              setError(`Failed to create entry: ${insertError.message}`)
            }
            hasError = true
          } else {
            setEditingEntry(newEntry)
          }
        }

        // Only redirect if no errors occurred
        if (!hasError) {
          // Trigger first post modal if this was a publish action
          if (publish) {
            await triggerFirstPostModal(user.id)
          }

          // Redirect to the published entry so user can see and share it
          if (publish && editingEntry) {
            router.push(`/d/${editingEntry.id}`)
          } else {
            // For drafts, go back to dashboard
            router.push('/dashboard')
          }
        }
      } catch (error) {
        console.error("Unexpected error:", error)
        setError("An unexpected error occurred")
      }
    })
  }

  const handleDelete = () => {
    if (!editingEntry) return

    deleteConfirm.showConfirm(async () => {
      const { error: deleteError } = await supabase
        .from("diary_entries")
        .delete()
        .eq("id", editingEntry.id)
        .eq("user_id", user.id)

      if (deleteError) {
        setError("Failed to delete entry")
        throw new Error("Failed to delete entry")
      }

      router.push('/dashboard')
    })
  }

  const handleCreatePartTwo = async () => {
    if (!user || !editingEntry) return

    setSaving(true)
    try {
      // First, save the current entry
      await handleSave(true) // Publish current entry

      // Create Part 2 with similar title
      const partTwoTitle = editingEntry.title.includes("Part 1")
        ? editingEntry.title.replace("Part 1", "Part 2")
        : `${editingEntry.title} - Part 2`

      // Navigate to new entry creation with pre-filled title
      router.push(`/write/diary?part2=true&title=${encodeURIComponent(partTwoTitle)}`)
    } catch {
      setError("Failed to create Part 2")
    } finally {
      setSaving(false)
    }
  }

  // Simple auto-save function for photo upload
  const handleAutoSaveForPhotos = async (): Promise<string | null> => {
    if (!user) {
      return null
    }

    try {
      if (editingEntry) {
        // Just return existing entry ID, no need to update
        return editingEntry.id
      } else {
        // Create new entry as draft - allow without title for photo uploads
        const entryTitle = title.trim() || "Untitled Entry"

        const { data: newEntry, error } = await supabase
          .from("diary_entries")
          .insert({
            title: entryTitle,
            body_md: content.trim() || "", // Allow empty content for photo-only posts
            is_free: isFree,
            is_hidden: true, // Always save as draft for auto-save
            user_id: user.id
          })
          .select()
          .single()

        if (error) {
          console.error("Auto-save error:", error)
          throw new Error("Failed to save entry")
        }

        setEditingEntry(newEntry)
        // Update URL to edit mode
        window.history.replaceState({}, '', `/write/diary?edit=${newEntry.id}`)

        // If we used a placeholder title, update the title state to match
        if (!title.trim()) {
          setTitle(entryTitle)
        }

        return newEntry.id
      }
    } catch (error: any) {
      console.error('Auto-save for photos failed:', error)
      throw new Error(error?.message || 'Failed to save entry')
    }
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className={`min-h-screen transition-all duration-1000 ${
      zenMode
        ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 zen-mode'
        : 'bg-gradient-to-br from-gray-50 via-white to-gray-50'
    }`}>



      {/* Flow State Indicator */}
      {isInFlow && (
        <div className="fixed top-20 left-4 z-40 flex items-center gap-2 px-3 py-2 bg-green-500/20 backdrop-blur-md rounded-full text-green-700 text-sm font-medium">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          In Flow • {Math.round(typingVelocity)} WPM
        </div>
      )}

      <div className={`max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 transition-all duration-700 ${
        zenMode ? 'max-w-3xl' : ''
      }`}>

        {/* Header - Adaptive */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 gap-4 transition-all duration-700 ${
          zenMode ? 'opacity-30 hover:opacity-100' : 'opacity-100'
        } ${isInFlow ? 'opacity-50' : 'opacity-100'}`}>
          <div>
            <h1 className={`font-serif transition-all duration-500 ${
              zenMode
                ? 'text-xl text-white/90'
                : 'text-2xl sm:text-3xl text-gray-800'
            }`}>
              {editingEntry ? 'Edit Entry' : 'Write New Entry'}
            </h1>
            <p className={`font-serif mt-1 text-sm sm:text-base transition-all duration-500 ${
              zenMode
                ? 'text-white/60'
                : 'text-gray-600'
            }`}>
              {zenMode ? 'Focus. Flow. Create.' : 'Share your thoughts with your readers'}
            </p>
          </div>
          {/* Smart Controls */}
          <div className="flex items-center gap-3">
            {/* Theme Toggle */}
            <button
              onClick={() => setZenMode(!zenMode)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-500 ${
                zenMode
                  ? 'text-white/90 bg-slate-700/50 hover:bg-slate-600/50 border border-slate-600/30'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-200'
              }`}
              title={zenMode ? 'Switch to Bright Mode' : 'Switch to Dark Mode'}
            >
              {zenMode ? (
                <>
                  <span>☀️</span>
                  <span className="hidden sm:inline">Bright</span>
                </>
              ) : (
                <>
                  <span>🌙</span>
                  <span className="hidden sm:inline">Dark</span>
                </>
              )}
            </button>

            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-500 ${
                zenMode
                  ? 'text-white/80 bg-slate-700/50 hover:bg-slate-600/50'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
              } ${showPreview ? 'ring-2 ring-blue-500/30' : ''}`}
            >
              {showPreview ? '✏️ Edit' : '👁️ Preview'}
            </button>

            <Link
              href="/dashboard"
              className={`font-medium text-sm sm:text-base transition-all duration-500 ${
                zenMode ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              ← Dashboard
            </Link>
          </div>
        </div>

        {/* Revolutionary Writing Interface */}
        <div className={`rounded-2xl shadow-sm transition-all duration-1000 ${
          zenMode
            ? 'bg-slate-800/50 backdrop-blur-xl border border-slate-700/30'
            : 'bg-white/90 backdrop-blur-sm border border-gray-100/50'
        } ${isInFlow ? 'shadow-xl shadow-blue-500/10 ring-1 ring-blue-500/20' : 'shadow-sm'}`} data-tutorial="entry-form">

          {/* Adaptive Header */}
          <div className={`transition-all duration-700 ${
            zenMode
              ? 'p-6 sm:p-8 border-b border-slate-700/30'
              : 'p-4 sm:p-6 border-b border-gray-200'
          } ${zenMode && isInFlow ? 'opacity-20 hover:opacity-100' : 'opacity-100'}`}>

            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
              {/* Revolutionary Title Input */}
              <input
                type="text"
                placeholder={zenMode ? "Your story begins..." : "Entry title..."}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className={`flex-1 font-serif border-none outline-none bg-transparent transition-all duration-500 ${
                  zenMode
                    ? 'text-xl sm:text-3xl text-white/90 placeholder-white/30'
                    : 'text-xl sm:text-2xl text-gray-900 placeholder-gray-400'
                } ${isInFlow ? 'text-blue-900' : ''}`}
                maxLength={200}
              />

              {/* Adaptive Timestamp */}
              <div className={`text-right text-sm font-mono shrink-0 transition-all duration-500 ${
                zenMode ? 'text-white/50' : 'text-gray-500'
              }`}>
                <div>{currentTime.toLocaleDateString()}</div>
                <div>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                {editingEntry && (
                  <div className="text-xs mt-1 space-y-0.5">
                    <div>Created: {new Date(editingEntry.created_at).toLocaleDateString()}</div>
                    {editingEntry.edited_at && (
                      <div>Edited: {new Date(editingEntry.edited_at).toLocaleDateString()}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Revolutionary Writing Area */}
          <div className={`transition-all duration-700 ${
            zenMode ? 'p-8 sm:p-12' : 'p-4 sm:p-6'
          }`}>

            {/* The Sacred Writing Space */}
            <div className="relative">
              {showPreview ? (
                /* Smart Typography Preview */
                <div className={`transition-all duration-700 ${
                  zenMode ? 'min-h-[70vh]' : 'min-h-[24rem] sm:min-h-[32rem]'
                }`}>
                  <SmartTypography
                    content={content}
                    zenMode={zenMode}
                    isPreview={true}
                    isDesktop={deviceCapabilities.isDesktop}
                    enableAdvancedFeatures={deviceCapabilities.shouldUseAdvancedAnimations}
                    className="w-full"
                  />
                  {!content && (
                    <div className={`text-center py-20 ${
                      zenMode ? 'text-white/40' : 'text-gray-400'
                    }`}>
                      <p className="text-lg font-serif">Your beautifully formatted story will appear here...</p>
                    </div>
                  )}
                </div>
              ) : (
                /* Writing Interface */
                <>
                  <textarea
                    placeholder={zenMode ? "Let your thoughts flow..." : "Start writing your diary entry..."}
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    className={`w-full font-serif leading-relaxed border-none outline-none bg-transparent resize-none transition-all duration-700 ${
                      zenMode
                        ? 'h-[70vh] text-lg sm:text-xl text-white/90 placeholder-white/30 caret-white'
                        : 'h-64 sm:h-96 text-base sm:text-lg text-gray-900 placeholder-gray-400 caret-gray-900'
                    } ${isInFlow ? 'text-blue-900 leading-loose caret-blue-900' : ''}`}
                    maxLength={20000}
                    style={{
                      lineHeight: isInFlow ? '2' : '1.75',
                      fontSize: zenMode ? (isInFlow ? '1.25rem' : '1.125rem') : '1rem',
                      caretColor: zenMode ? '#ffffff' : (isInFlow ? '#1e3a8a' : '#111827')
                    }}
                  />

                  {/* Flow State Breathing Effect */}
                  {isInFlow && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5 animate-pulse"></div>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Adaptive Stats Bar */}
            <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-2 transition-all duration-700 ${
              zenMode && isInFlow ? 'opacity-20 hover:opacity-100' : 'opacity-100'
            }`}>

              {/* Smart Typography Stats */}
              <div className={`text-sm transition-all duration-500 ${
                zenMode
                  ? content.length > 19000 ? 'text-red-400' : content.length > 18000 ? 'text-yellow-400' : 'text-white/50'
                  : content.length > 19000 ? 'text-red-600' : content.length > 18000 ? 'text-yellow-600' : 'text-gray-500'
              }`}>
                {zenMode ? (
                  <div className="flex items-center gap-4">
                    <span>{wordCount} words</span>
                    <span>•</span>
                    <span>{Math.ceil(wordCount / 200)} min read</span>
                    {typographyMetrics && (
                      <>
                        <span>•</span>
                        <span>{typographyMetrics.readabilityScore}</span>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-4">
                    <span>{content.length}/20,000 characters</span>
                    {typographyMetrics && (
                      <>
                        <span>•</span>
                        <span>{typographyMetrics.paragraphs} paragraphs</span>
                        <span>•</span>
                        <span>{typographyMetrics.readabilityScore}</span>
                      </>
                    )}
                    {content.length > 19000 && (
                      <span className="ml-2 font-medium">
                        ⚠️ Near character limit!
                      </span>
                    )}
                  </div>
                )}
              </div>


            </div>
          </div>

          {/* Part 2 Option */}
          {showPartTwoOption && (
            <div className="p-4 border-t border-yellow-200 bg-yellow-50">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-yellow-800 mb-1">
                    Approaching Character Limit
                  </h4>
                  <p className="text-xs text-yellow-700">
                    Consider creating a Part 2 to continue your story without losing content.
                  </p>
                </div>
                <button
                  onClick={handleCreatePartTwo}
                  disabled={saving || !editingEntry}
                  className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 disabled:opacity-50"
                >
                  Create Part 2
                </button>
              </div>
            </div>
          )}

          {/* Adaptive Options Panel */}
          <div className={`transition-all duration-700 ${
            zenMode
              ? 'p-6 sm:p-8 border-t border-slate-700/30'
              : 'p-4 sm:p-6 border-t border-gray-200'
          } ${zenMode && isInFlow ? 'opacity-30 hover:opacity-100' : 'opacity-100'}`}>

            <div className="space-y-4 mb-6">
              {/* Access Type Selection */}
              <div className="space-y-3">
                <label className={`block text-sm font-medium ${
                  zenMode ? 'text-white/80' : 'text-gray-700'
                }`}>
                  Entry Access
                </label>

                <div className="space-y-2">
                  <label className="flex items-center group cursor-pointer">
                    <input
                      type="radio"
                      name="accessType"
                      checked={!isFree}
                      onChange={() => setIsFree(false)}
                      className={`mr-3 transition-all duration-300 ${
                        zenMode ? 'text-blue-400 border-white/30' : 'text-blue-600 border-gray-300'
                      }`}
                    />
                    <span className={`text-sm sm:text-base transition-all duration-300 group-hover:scale-105 ${
                      zenMode ? 'text-white/80' : 'text-gray-700'
                    }`}>
                      <strong>Paid Access</strong> - Subscribers only
                    </span>
                  </label>

                  <label className="flex items-center group cursor-pointer">
                    <input
                      type="radio"
                      name="accessType"
                      checked={isFree}
                      onChange={() => setIsFree(true)}
                      className={`mr-3 transition-all duration-300 ${
                        zenMode ? 'text-green-400 border-white/30' : 'text-green-600 border-gray-300'
                      }`}
                    />
                    <span className={`text-sm sm:text-base transition-all duration-300 group-hover:scale-105 ${
                      zenMode ? 'text-white/80' : 'text-gray-700'
                    }`}>
                      <strong>Free Access</strong> - Anyone can read (donations accepted)
                    </span>
                  </label>
                </div>
              </div>

              {/* Draft Option */}
              <label className="flex items-center group cursor-pointer">
                <input
                  type="checkbox"
                  checked={isHidden}
                  onChange={(e) => setIsHidden(e.target.checked)}
                  className={`mr-3 rounded transition-all duration-300 ${
                    zenMode ? 'text-white border-white/30' : 'text-gray-600 border-gray-300'
                  }`}
                />
                <span className={`text-sm sm:text-base transition-all duration-300 group-hover:scale-105 ${
                  zenMode ? 'text-white/80' : 'text-gray-700'
                }`}>
                  Save as draft (hidden from readers)
                </span>
              </label>


            </div>

            {error && (
              <div className={`mb-4 p-3 rounded-xl transition-all duration-500 ${
                zenMode
                  ? 'bg-red-900/20 border border-red-800/30 text-red-300'
                  : 'bg-red-50 border border-red-200 text-red-600'
              }`}>
                <p className="text-sm">{error}</p>
              </div>
            )}

          </div>

          {/* Photo Upload Section */}
          <div data-tutorial="media-upload">
            <PhotoUpload
              entryId={editingEntry?.id}
              hasVideo={hasVideo}
              onPhotosChange={(photos) => {
                // Optional: Handle photo changes if needed
                console.log('Photos updated:', photos)
              }}
              onMediaTypeChange={setHasPhotos}
              zenMode={zenMode}
            />
          </div>

          {/* Video Upload Section */}
          <VideoUpload
            key="video-upload-stable"
            postId={editingEntry?.id}
            hasPhotos={hasPhotos}
            onVideoUploaded={(videoId) => {
              console.log('Video uploaded:', videoId)
            }}
            onMediaTypeChange={setHasVideo}
            zenMode={zenMode}
            onCreateEntry={async () => {
              // Create entry for video - simple, no validation
              if (!editingEntry && user) {
                const { data: newEntry, error } = await supabase
                  .from('diary_entries')
                  .insert({
                    title: title.trim() || 'Untitled Entry',
                    body_md: content.trim() || '',
                    user_id: user.id,
                    is_free: isFree,
                    is_hidden: true
                  })
                  .select()
                  .single()

                if (!error && newEntry) {
                  setEditingEntry(newEntry)
                  return newEntry.id
                }
              }
              return editingEntry?.id || null
            }}
          />

          {/* Story Ventures Section */}
          <div className={`border-t p-6 ${
            zenMode ? 'border-slate-700/30' : 'border-gray-200'
          }`}>
            <GoalSettingForm
              onGoalSet={setStoryVentureGoal}
              initialValues={storyVentureGoal || undefined}
              className="mb-6"
              zenMode={zenMode}
            />
          </div>

          {/* Publish Section - Moved to bottom after photos */}
          <div className={`border-t p-6 ${
            zenMode ? 'border-slate-700/30' : 'border-gray-200'
          }`}>
            <div className="mb-4">
              <h3 className={`text-lg font-medium mb-2 ${
                zenMode ? 'text-white/90' : 'text-gray-900'
              }`}>Ready to Publish?</h3>
              <p className={`text-sm ${
                zenMode ? 'text-white/70' : 'text-gray-600'
              }`}>
                Add any photos above, then publish your entry to make it visible to your audience.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
              <div className="flex flex-col sm:flex-row gap-3">
                {/* Save Draft */}
                <Button
                  onClick={() => handleSave(false)}
                  isLoading={isPending}
                  variant="secondary"
                  className={`transition-all duration-500 ${
                    zenMode
                      ? 'text-white bg-slate-600/80 hover:bg-slate-500/80 border-slate-500/50 font-medium'
                      : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
                  } ${isInFlow ? 'shadow-lg shadow-blue-500/20' : ''}`}
                >
                  {zenMode ? '💾 Save' : 'Save Draft'}
                </Button>

                {/* Publish - Flow State Enhanced */}
                <Button
                  onClick={() => handleSave(true)}
                  isLoading={isPending}
                  data-tutorial="save-button"
                  className={`transition-all duration-500 ${
                    zenMode
                      ? 'bg-white text-slate-900 hover:bg-gray-100 font-medium'
                      : 'bg-gray-800 text-white hover:bg-gray-700'
                  } ${isInFlow ? 'shadow-xl shadow-purple-500/30 ring-2 ring-purple-500/20' : ''}`}
                >
                  {zenMode ? '✨ Publish' : 'Publish'}
                </Button>
              </div>

              {/* Delete - Contextual */}
              {editingEntry && (
                <Button
                  onClick={handleDelete}
                  disabled={isPending || deleteConfirm.isLoading}
                  variant="destructive"
                  className={`transition-all duration-500 ${
                    zenMode
                      ? 'text-red-400 bg-red-900/20 hover:bg-red-900/30 border-red-800/30'
                      : 'text-red-600 bg-red-50 hover:bg-red-100'
                  }`}
                >
                  {zenMode ? '🗑️' : 'Delete Entry'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteConfirm.isOpen}
        onClose={deleteConfirm.handleClose}
        onConfirm={deleteConfirm.handleConfirm}
        isLoading={deleteConfirm.isLoading}
        entryTitle={editingEntry?.title}
        title="Delete Entry"
        message="Are you sure you want to delete this entry? This action cannot be undone and all associated photos and comments will also be deleted."
        confirmText="Delete Forever"
      />

      {/* First Post Modal */}
      {user && (
        <FirstPostModal
          userId={user.id}
          userName={user.user_metadata?.name || user.email}
        />
      )}
    </div>
  )
}

export default function WritePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    }>
      <WritePageContent />
    </Suspense>
  )
}