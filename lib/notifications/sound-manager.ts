/**
 * OnlyDiary Sound Notification Manager
 * Handles playing notification sounds for payments and messages
 */

export class SoundManager {
  private static instance: SoundManager
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()
  private isEnabled: boolean = true

  private constructor() {
    // Initialize audio context on first user interaction
    this.initializeAudioContext()
  }

  static getInstance(): SoundManager {
    if (!SoundManager.instance) {
      SoundManager.instance = new SoundManager()
    }
    return SoundManager.instance
  }

  private async initializeAudioContext() {
    if (typeof window === 'undefined') return

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // Preload sounds
      await this.preloadSounds()
    } catch (error) {
      console.warn('Audio context initialization failed:', error)
    }
  }

  private async preloadSounds() {
    const soundFiles = {
      payment: '/sounds/payment-notification.mp3',
      message: '/sounds/message-notification.mp3'
    }

    for (const [key, url] of Object.entries(soundFiles)) {
      try {
        const response = await fetch(url)
        const arrayBuffer = await response.arrayBuffer()
        
        if (this.audioContext) {
          const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
          this.sounds.set(key, audioBuffer)
        }
      } catch (error) {
        console.warn(`Failed to preload sound ${key}:`, error)
      }
    }
  }

  async playPaymentNotification() {
    await this.playSound('payment')
  }

  async playMessageNotification() {
    await this.playSound('message')
  }

  private async playSound(soundKey: string) {
    if (!this.isEnabled || !this.audioContext || !this.sounds.has(soundKey)) {
      return
    }

    try {
      // Resume audio context if suspended (required by browser policies)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      const audioBuffer = this.sounds.get(soundKey)
      if (!audioBuffer) return

      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()
      
      source.buffer = audioBuffer
      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      // Set volume (0.0 to 1.0)
      gainNode.gain.value = 0.7
      
      source.start()
    } catch (error) {
      console.warn(`Failed to play ${soundKey} sound:`, error)
    }
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    
    // Store preference in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('onlydiary-sounds-enabled', enabled.toString())
    }
  }

  isNotificationEnabled(): boolean {
    if (typeof window === 'undefined') return true
    
    const stored = localStorage.getItem('onlydiary-sounds-enabled')
    return stored !== null ? stored === 'true' : true
  }

  // Initialize on first user interaction
  async initializeOnUserInteraction() {
    if (!this.audioContext) {
      await this.initializeAudioContext()
    }
  }
}

// Convenience functions for easy use throughout the app
export const playPaymentSound = () => SoundManager.getInstance().playPaymentNotification()
export const playMessageSound = () => SoundManager.getInstance().playMessageNotification()
export const initializeSounds = () => SoundManager.getInstance().initializeOnUserInteraction()
