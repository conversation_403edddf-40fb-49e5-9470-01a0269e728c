"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Target, Calendar, Eye, EyeOff, Info } from "lucide-react"

interface GoalSettingFormProps {
  onGoalSet: (goal: StoryVentureGoal) => void
  initialValues?: Partial<StoryVentureGoal>
  className?: string
  zenMode?: boolean
}

export interface StoryVentureGoal {
  isEnabled: boolean
  goalAmount: number
  deadline?: Date
  description: string
  showPublicly: boolean
}

export function GoalSettingForm({ onGoalSet, initialValues, className = "", zenMode = false }: GoalSettingFormProps) {
  const [isEnabled, setIsEnabled] = useState(initialValues?.isEnabled || false)
  const [goalAmount, setGoalAmount] = useState(initialValues?.goalAmount?.toString() || "")
  const [deadline, setDeadline] = useState(
    initialValues?.deadline ? initialValues.deadline.toISOString().split('T')[0] : ""
  )
  const [description, setDescription] = useState(initialValues?.description || "")
  const [showPublicly, setShowPublicly] = useState(initialValues?.showPublicly ?? true)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleToggle = (enabled: boolean) => {
    setIsEnabled(enabled)

    if (!enabled) {
      // If disabling, immediately notify parent
      onGoalSet({
        isEnabled: false,
        goalAmount: 0,
        description: "",
        showPublicly: true
      })
      return
    }

    // If enabling, validate and submit the form
    const amount = parseFloat(goalAmount)
    if (isNaN(amount) || amount < 1) {
      // If no valid amount, just enable without goal for now
      onGoalSet({ isEnabled: true })
      return
    }

    onGoalSet({
      isEnabled: true,
      goalAmount: amount,
      deadline: deadline ? new Date(deadline) : undefined,
      description: description.trim(),
      showPublicly
    })
  }

  const minDate = new Date()
  minDate.setDate(minDate.getDate() + 1) // Tomorrow
  const maxDate = new Date()
  maxDate.setFullYear(maxDate.getFullYear() + 1) // One year from now

  return (
    <div className={`rounded-lg p-6 transition-all duration-1000 ${
      zenMode
        ? 'bg-slate-800/30 border border-slate-700/30 backdrop-blur-xl'
        : 'bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200'
    } ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <Target className={`w-6 h-6 ${zenMode ? 'text-purple-400' : 'text-purple-600'}`} />
        <div>
          <h3 className={`text-lg font-serif ${zenMode ? 'text-purple-300' : 'text-purple-800'}`}>Story Ventures</h3>
          <p className={`text-sm ${zenMode ? 'text-purple-200' : 'text-purple-600'}`}>
            Your most ambitious ideas deserve more than just a diary entry. Story Ventures lets you turn creative dreams into funded projects with support from readers who believe in your vision.
          </p>
        </div>
      </div>

      {/* Enable Toggle */}
      <div className="mb-6">
        <label className="flex items-center gap-3 cursor-pointer">
          <input
            type="checkbox"
            checked={isEnabled}
            onChange={(e) => handleToggle(e.target.checked)}
            className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
          />
          <span className={`font-medium ${zenMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Enable Story Ventures for this entry
          </span>
        </label>
        <p className={`text-sm mt-1 ml-8 ${zenMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Turn this diary entry into a crowdfunded creative project
        </p>
      </div>

      {isEnabled && (
        <div className="space-y-6">
          {/* Funding Goal */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${zenMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Funding Goal *
            </label>
            <div className="relative">
              <span className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${zenMode ? 'text-gray-400' : 'text-gray-500'}`}>$</span>
              <input
                type="number"
                value={goalAmount}
                onChange={(e) => setGoalAmount(e.target.value)}
                placeholder="1000"
                min="1"
                step="1"
                className={`w-full pl-8 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  zenMode
                    ? 'bg-slate-700/50 border-slate-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
            <p className={`text-xs mt-1 ${zenMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Set a realistic funding target for your creative project
            </p>
          </div>

          {/* Project Description */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${zenMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Project Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what you'll create with the funding and why readers should support this story..."
              rows={3}
              maxLength={500}
              className={`w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                zenMode
                  ? 'bg-slate-700/50 border-slate-600 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
            <p className={`text-xs mt-1 ${zenMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {description.length}/500 characters
            </p>
          </div>

          {/* Advanced Options Toggle */}
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`text-sm font-medium flex items-center gap-1 ${
              zenMode
                ? 'text-purple-400 hover:text-purple-300'
                : 'text-purple-600 hover:text-purple-700'
            }`}
          >
            {showAdvanced ? 'Hide' : 'Show'} advanced options
            <Info className="w-4 h-4" />
          </button>

          {showAdvanced && (
            <div className={`space-y-4 rounded-lg p-4 border ${
              zenMode
                ? 'bg-slate-700/30 border-slate-600/50'
                : 'bg-white/50 border-purple-100'
            }`}>
              {/* Deadline */}
              <div>
                <label className={`block text-sm font-medium mb-2 flex items-center gap-2 ${
                  zenMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  <Calendar className="w-4 h-4" />
                  Campaign Deadline (Optional)
                </label>
                <input
                  type="date"
                  value={deadline}
                  onChange={(e) => setDeadline(e.target.value)}
                  min={minDate.toISOString().split('T')[0]}
                  max={maxDate.toISOString().split('T')[0]}
                  className={`w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                    zenMode
                      ? 'bg-slate-700/50 border-slate-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
                <p className={`text-xs mt-1 ${zenMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Leave empty for ongoing funding without deadline
                </p>
              </div>

              {/* Public Visibility */}
              <div>
                <label className={`block text-sm font-medium mb-3 ${
                  zenMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Funding Transparency
                </label>
                <div className="space-y-3">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="visibility"
                      checked={showPublicly}
                      onChange={() => setShowPublicly(true)}
                      className="w-4 h-4 text-purple-600"
                    />
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4 text-green-600" />
                      <span className={`text-sm ${zenMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        <strong>Public</strong> - Show funding progress, backer count, and goal
                      </span>
                    </div>
                  </label>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="visibility"
                      checked={!showPublicly}
                      onChange={() => setShowPublicly(false)}
                      className="w-4 h-4 text-purple-600"
                    />
                    <div className="flex items-center gap-2">
                      <EyeOff className={`w-4 h-4 ${zenMode ? 'text-gray-400' : 'text-gray-600'}`} />
                      <span className={`text-sm ${zenMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        <strong>Private</strong> - Only you can see funding details
                      </span>
                    </div>
                  </label>
                </div>
                <div className={`mt-3 p-3 rounded-lg border ${
                  zenMode
                    ? 'bg-blue-900/30 border-blue-700/50'
                    : 'bg-blue-50 border-blue-200'
                }`}>
                  <p className={`text-xs ${zenMode ? 'text-blue-200' : 'text-blue-700'}`}>
                    <strong>Why Public Works Better:</strong> Transparency builds trust, creates social proof,
                    and encourages more backers. Most successful crowdfunding campaigns are public.
                  </p>
                </div>
              </div>
            </div>
          )}

        </div>
      )}
    </div>
  )
}
